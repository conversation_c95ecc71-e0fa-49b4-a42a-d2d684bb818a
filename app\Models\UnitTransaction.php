<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use App\Models\DocMultyUnit;
class UnitTransaction extends Model
{
    protected $fillable = [
        'unit_id',
        'site_id',
        'status',
        'mr_date',
        'wo_number',
        'do_number',
        'do_date',
        'tanggalstart',
        'tanggalend',
        'noireq',
        'noBA',
        'pekerjaan',
        'HMKM',
        'SHIFT',
        'LOKASI',
        'po_number',
        'issue_nomor',
        'remarks',
        'sales_notes',
        'noSPB',
        'updated_at',
        'contact',
        'phone',
        'customer',
        'sitework',
        'attachment_path',
        'actual_out_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'mr_date' => 'date',
        'do_date' => 'date',
        'tanggalstart' => 'date',
        'tanggalend' => 'date',
        'actual_out_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    public function parts()
    {
        return $this->hasMany(UnitTransactionPart::class)->orderBy('id');
    }

    /**
     * Get the invoice associated with this unit transaction
     * This is a helper method to get the latest invoice
     */
    public function getLatestInvoice()
    {
        return $this->invoices()->latest()->first();
    }

    /**
     * Get all invoices associated with this unit transaction
     */
    public function invoices()
    {
        return $this->belongsToMany(Invoice::class, 'invoice_unit_transactions')
            ->withTimestamps();
    }

    /**
     * Check if this unit transaction has been invoiced
     */
    public function isInvoiced()
    {
        return $this->invoices()->exists();
    }

    /**
     * Get the BAPP documents associated with this unit transaction
     */
    public function docMultyUnits()
    {
        return DocMultyUnit::whereJsonContains('unit_transaction_ids', $this->id)->get();
    }

    /**
     * Check if this unit transaction has a BAPP document
     */
    public function hasBAPP()
    {
        return DocMultyUnit::whereJsonContains('unit_transaction_ids', $this->id)->exists();
    }
}